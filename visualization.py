import matplotlib.pyplot as plt
import os
import os.path as path
import numpy as np
import matplotlib.patches as ptchs
import argparse
import seaborn as sns
from utils import *


plt.rcParams["font.family"] = "Arial"
plt.rcParams['font.size'] = 10
STEP_SIZE = 10
WINDOW_SIZE = 20

import numpy as np
import matplotlib.patches as ptchs


def plot_raster(ax, cells_of_trials, start_point, color=None,
                          ax_title=None, cell_num=10, inf_value=10000):
    """
    Plots a raster of a random trial from a subset of cells, sorted by the first
    spike time after a start point, using matplotlib.eventplot.
    """
    # --- 1. Data Preparation (same as original) ---
    sorted_trials = []
    # For each cell, sample one trial and find its first spike for sorting
    for trials in cells_of_trials.values():
        if not trials: continue  # Skip if a cell has no trials

        sample_trial = trials[np.random.choice(len(trials))]

        spikes_after_start = sample_trial[sample_trial >= start_point]
        first_spike_time = np.min(spikes_after_start) if spikes_after_start.size > 0 else inf_value

        sorted_trials.append((sample_trial, first_spike_time))

    # Sort all sampled trials by the first spike time
    sorted_trials.sort(key=lambda x: x[1])

    # --- 2. Select Data for Plotting ---
    num_available = len(sorted_trials)
    if num_available == 0:
        print("Warning: No trials with spikes found to plot.")

    # Subsample the sorted trials to get the desired number for the plot
    plot_indices = np.linspace(0, num_available - 1, min(cell_num, num_available), dtype=int)
    events_to_plot = [sorted_trials[i][0] for i in plot_indices]
    actual_plot_num = len(events_to_plot)

    # --- 3. Plotting with eventplot ---
    if actual_plot_num > 0:
        # y-positions for each trial, starting from 1
        y_positions = np.arange(1, actual_plot_num + 1)
        ax.eventplot(
            events_to_plot,
            colors='black',
            lineoffsets=y_positions,
            linelengths=0.8,  # Controls the height of the tick marks
            alpha=0.7
        )

    # --- 4. Aesthetics (similar to original, with minor adjustments) ---
    ax.spines[['right', 'top']].set_visible(False)
    if color is not None and actual_plot_num > 0:
        # Rectangle patch corrected to match the number of plotted cells
        ax.add_patch(ptchs.Rectangle((1000, 0.5), 10, actual_plot_num,
                                     facecolor=color, alpha=0.3, edgecolor='none'))

    ax.set_xticks([900, 1000, 1100], ["-100", "0", "100"])
    ax.set_xlabel("Time (ms)")
    ax.set_ylabel("Cell #")
    ax.set_xlim(900, 1100)
    ax.set_ylim(0.5, actual_plot_num + 0.5)  # Clean y-limits
    if ax_title is not None:
        ax.set_title(ax_title, fontsize=5)



def plot_raster_all_trial(ax, cells_of_trials, start_point, color=None,
                                    ax_title=None, inf_value=10000):
    """
    Plots a raster of ALL trials from ALL cells, sorted by the first spike time
    after a start point, using matplotlib.eventplot.
    """
    # --- 1. Data Preparation (same as original) ---
    sorted_trials = []
    # Collect every trial from every cell
    for trials in cells_of_trials.values():
        for trial in trials:
            spikes_after_start = trial[trial >= start_point]
            first_spike_time = np.min(spikes_after_start) if spikes_after_start.size > 0 else inf_value
            sorted_trials.append((trial, first_spike_time))

    # Sort all trials by the first spike time
    sorted_trials.sort(key=lambda x: x[1])

    trial_num = len(sorted_trials)
    if trial_num == 0:
        print("Warning: No trials with spikes found to plot.")

    # Extract just the spike time arrays for plotting
    events_to_plot = [trial for trial, _ in sorted_trials]

    # --- 2. Plotting with eventplot ---
    if trial_num > 0:
        # y-positions for each trial, starting from 1
        y_positions = np.arange(1, trial_num + 1)
        ax.eventplot(
            events_to_plot,
            colors='black',
            lineoffsets=y_positions,
            linewidths=0.5,  # Thin lines to mimic dots
        )

    # --- 3. Aesthetics (similar to original, with minor adjustments) ---
    ax.spines[['right', 'top']].set_visible(False)
    if color is not None and trial_num > 0:
        # Rectangle patch corrected to match the total number of trials
        ax.add_patch(ptchs.Rectangle((1000, 0.5), 10, trial_num,
                                     facecolor=color, alpha=0.3, edgecolor='none'))

    ax.set_xticks([900, 1000, 1100], ["-100", "0", "100"])
    ax.set_xlabel("Time (ms)")
    ax.set_ylabel("Trial #")
    ax.set_xlim(850, 1150)
    ax.set_ylim(0.5, trial_num + 0.5)  # Clean y-limits
    if ax_title is not None:
        ax.set_title(ax_title, fontsize=5)


def calculate_firing_rate(ts, window_size, spike_series, baseline_range=None):
    firing_rates = []
    for t in ts:
        count = np.sum((spike_series >= (t - window_size / 2)) & (spike_series < (t + window_size / 2)))
        firing_rates.append(count / window_size)
    if baseline_range is not None:
        baseline_fr = np.sum((spike_series >= baseline_range[0]) &
                                (spike_series < baseline_range[1]))/(baseline_range[1]-baseline_range[0])
        return np.array(firing_rates)/baseline_fr
    else:
        return np.array(firing_rates) * 1000


def calculate_baseline_firing_rate(spike_series, baseline_range=(100, 1000)):
    baseline_fr = np.sum((spike_series >= baseline_range[0]) &
                                (spike_series < baseline_range[1]))/(baseline_range[1]-baseline_range[0])
    return baseline_fr * 1000


def plot_firing_rate(ax, data_cells_of_trials, data_label, data_color, start_point, **kwargs):
    ts = np.arange(800, 1200, STEP_SIZE)

    all_fr = np.stack([calculate_firing_rate(ts, WINDOW_SIZE, np.concatenate(trials), baseline_range=(100, 1000))
                       for trials in data_cells_of_trials.values()], axis=0)
    mean_fr, sem_fr = np.mean(all_fr, axis=0), np.std(all_fr, axis=0)/np.sqrt(all_fr.shape[0])
    ax.plot(ts - start_point, mean_fr, color=data_color, lw=1, label=data_label, **kwargs)
    ax.fill_between(ts - start_point, mean_fr + sem_fr, mean_fr - sem_fr, color=data_color, alpha=0.2, lw=0)
    ax.spines[['right', 'top']].set_visible(False)
    ax.axhline(y=1, ls='--', color='gray', alpha=0.4, lw=1)
    ax.axvline(x=0, ls='--', color='gray', alpha=0.4, lw=1)
    ax.set_xticks((-100, 0, 100))
    ax.set_xlabel("Time (ms)")
    ax.set_ylabel("Norm. FR")
    ax.set_xlim(-100, 150)
    ax.set_ylim(0, 1.6)

def plot_raw_firing_rate(ax, data_cells_of_trials, data_label, data_color, start_point, **kwargs):
    ts = np.arange(800, 1200, STEP_SIZE)
    all_fr = np.stack([calculate_firing_rate(ts, WINDOW_SIZE, np.concatenate(trials), baseline_range=None)/len(trials)
                       for trials in data_cells_of_trials.values()], axis=0)
    mean_fr, sem_fr = np.mean(all_fr, axis=0), np.std(all_fr, axis=0)/np.sqrt(all_fr.shape[0])
    ax.plot(ts - start_point, mean_fr, color=data_color, lw=1, label=data_label, **kwargs)
    ax.fill_between(ts - start_point, mean_fr + sem_fr, mean_fr - sem_fr, color=data_color, alpha=0.2, lw=0)
    ax.spines[['right', 'top']].set_visible(False)
    ax.axvline(x=0, ls='--', color='gray', alpha=0.4, lw=1)
    ax.set_xticks((-100, 0, 100))
    ax.set_xlabel("Time (ms)")
    ax.set_ylabel("FR (Hz)")
    ax.set_xlim(-100, 150)
    ax.set_ylim(0, 50)


def calculate_cdf(cells_of_trials, start_point, inf_value=10000):
    first_spikes = []
    for trials in cells_of_trials.values():
        trial_first_spikes = []
        for trial in trials:
            if np.sum(trial >= start_point) > 0:
                trial_first_spikes.append(np.min(trial[trial >= start_point]))
            else:
                trial_first_spikes.append(inf_value)
        first_spikes += trial_first_spikes
        # first_spikes.append(np.mean(trial_first_spikes))
    first_spikes = np.array(sorted(first_spikes))
    p = 1. * np.arange(len(first_spikes)) / (len(first_spikes) - 1)
    return first_spikes, p


def plot_time2recover(ax, data, start_point, **kwargs):
    first_spikes, p = calculate_cdf(data, start_point)
    ax.plot(first_spikes - start_point, p, **{**{"lw": 2, "alpha": 0.7}, **kwargs})
    ax.legend(frameon=False, fontsize='small', handlelength=1, labelspacing=0.2, loc='best')
    ax.spines[['right', 'top']].set_visible(False)
    ax.set_xticks((0, 80, 160))
    ax.set_yticks((0, 0.25, 0.5,), ("0", "25", "50",))
    ax.set_xlabel("Time after Stim (ms)")
    ax.set_ylabel("% cell recovered")
    ax.set_xlim(0, 160)
    ax.set_ylim(0, 0.5)
    ax.set_title("Time to recover")


def plot_pause_time_hist(ax, data, label, color, start_point):
    first_spikes, p = calculate_cdf(data, start_point, inf_value=np.nan)

    bins = np.arange(0, 400, 20)
    sns.histplot(first_spikes - start_point, bins=bins, label=label, color=color,
                 stat='density', kde=True, ax=ax,
                 linewidth=0, line_kws={'linewidth': 1})
    # ax.legend(frameon=False, fontsize='small', labelspacing=0.2)
    ax.spines[['right', 'top']].set_visible(False)
    ax.set_xticks([0, 200, 400])
    ax.set_xlabel("Time (ms)")
    ax.set_ylabel("% cell")
    ax.set_xlim(0, 400)
    ax.set_ylim(0, 0.025)
    ax.set_title("Pause time")


def plot_result_comparison(save_name, target,):
    baseline_GPe = xlsx_reader(path.join('bio_data', 'OnePulse.xlsx'), "GPe naive")
    baseline_Str = xlsx_reader(path.join('bio_data', 'OnePulse.xlsx'), "D1 naive")
    baseline_noHCN_GPe = xlsx_reader(path.join('bio_data', 'OnePulse.xlsx'), "GPeZD")
    baseline_noHCN_Str = xlsx_reader(path.join('bio_data', 'OnePulse.xlsx'), "D1ZD")
    target_GPe = xlsx_reader(path.join('simulation_result', f'{target}.xlsx'), "GPe")
    target_Str = xlsx_reader(path.join('simulation_result', f'{target}.xlsx'), "Str")
    target_noHCN_GPe = xlsx_reader(path.join('simulation_result', '20250922_170508_zero_HCN_zero.xlsx'), "GPe")
    target_noHCN_Str = xlsx_reader(path.join('simulation_result', '20250922_170508_zero_HCN_zero.xlsx'), "Str")
    # print(baseline_Str)
    # print(baseline_noHCN_Str)
    # print(baseline_GPe)
    # print(baseline_noHCN_GPe)
    fig, axs = plt.subplots(4, 8, sharex='col', constrained_layout=True,
                            gridspec_kw={'width_ratios': [1, 1, 1.5, 1.5, 1.5, 1.5, 1.5, 3]}, figsize=(18, 8))
    baseline_start = 1008
    sim_start = 1004
    example_cell_num = 20

    def plot_row(ax_row, baseline_data, target_data, target_color, baseline_label, target_label, **kwargs):
        plot_raster(ax_row[0], baseline_data, baseline_start, 'gray', baseline_label, example_cell_num)
        plot_raster(ax_row[1], target_data, sim_start, target_color, target_label, example_cell_num)
        plot_raster_all_trial(ax_row[2], baseline_data, baseline_start, 'gray', baseline_label)
        plot_raster_all_trial(ax_row[3], target_data, sim_start, target_color, target_label,)

        plot_firing_rate(ax_row[4], baseline_data, baseline_label, "gray", baseline_start, **kwargs)
        plot_firing_rate(ax_row[4], target_data, target_label, target_color, sim_start, **kwargs)

        plot_raw_firing_rate(ax_row[5], baseline_data, baseline_label, "gray", baseline_start, **kwargs)
        plot_raw_firing_rate(ax_row[5], target_data, target_label, target_color, sim_start, **kwargs)

        plot_pause_time_hist(ax_row[6], baseline_data, baseline_label, "gray", baseline_start)
        plot_pause_time_hist(ax_row[6], target_data, target_label, target_color, sim_start)

        plot_time2recover(ax_row[7], baseline_data, baseline_start, label=baseline_label, color="gray", **kwargs)
        plot_time2recover(ax_row[7], target_data, sim_start, label=target_label, color=target_color, **kwargs)

    plot_row(axs[0], baseline_GPe, target_GPe, GPE_COLOR, "GPe data", "GPe sim")
    plot_row(axs[1], baseline_noHCN_GPe, target_noHCN_GPe, GPE_COLOR, "GPe data ZD", "GPe sim w/o HCN", ls='--', alpha=0.5)
    plot_row(axs[2], baseline_Str, target_Str, STR_COLOR, "Str data", "Str sim")
    plot_row(axs[3], baseline_noHCN_Str, target_noHCN_Str, STR_COLOR, "Str data ZD", "Str sim w/o HCN", ls='--', alpha=0.5)

    fig.savefig(path.join(path.dirname(__file__), "figures", f"{save_name}.jpg"),  dpi=300)
    plt.close(fig)


def plot_single(save_name, task_id):
    metrics = ("Vs", "Vd", "I_app",
               "I_HCN_som", "m_HCN_som", "g_HCN_som",
               "I_HCN_den", "m_HCN_den", "g_HCN_den",
               "I_TRPC3",
               "I_GABA_som", "g_GABA_som", "E_GABA_som", "D",
               "I_GABA_den", "g_GABA_den", "E_GABA_den", "F",
               )
    spike_times = csv_reader_trials(path.join('simulation_result', task_id, f'single.csv'))
    fig, axs = plt.subplots(len(metrics)+1, 3, sharex='col', sharey='row', figsize=(16, len(metrics)),
                            width_ratios=[2, 1, 1])

    for col_id in range(3):
        axs[0, col_id].eventplot(spike_times, )
        axs[0, col_id].spines[['right', 'top']].set_visible(False)
        axs[0, col_id].set_ylabel("Spikes")
        axs[0, col_id].axvline(x=1000, ls='--', color='gray', alpha=0.4, lw=1)

    for i, metric_name in enumerate(metrics):
        tmp_value = csv_reader_single(path.join('simulation_result', task_id, f'single_{metric_name}.csv'))
        ts = np.linspace(0, 2000, len(tmp_value))
        for col_id in range(3):
            axs[i+1, col_id].plot(ts, tmp_value)
            axs[i+1, col_id].spines[['right', 'top']].set_visible(False)
            axs[i+1, col_id].set_ylabel(metric_name, rotation=45)
            axs[i+1, col_id].axvline(x=1000, ls='--', color='gray', alpha=0.4, lw=1)

    axs[0, 1].set_xlim(800, 1200)
    axs[0, 2].set_xlim(950, 1050)
    axs[-1, 0].set_xlabel("Time [ms]")
    axs[-1, 1].set_xlabel("Time [ms]")
    axs[-1, 2].set_xlabel("Time [ms]")
    fig.savefig(path.join(path.dirname(__file__), "figures", f"{save_name}.jpg"), dpi=300)
    plt.close(fig)


def parse_args():
    parser = argparse.ArgumentParser(description="postprocess")
    parser.add_argument("-task_id", type=str, required=True)
    parser.add_argument("-single", action='store_true')
    parser.add_argument("-overwrite", action='store_true')
    return parser.parse_args()


def main(task_id, single, overwrite):
    if not single:  # check if task is a single simulation
        convert_csv2xlsx(task_id, overwrite=overwrite)
        for hcn_pos in ("som", "den", "zero"):
            if path.exists(path.join("simulation_result", f"{task_id}_HCN_{hcn_pos}.xlsx")):
                plot_result_comparison(f"{task_id}_HCN_{hcn_pos}",
                                       f"{task_id}_HCN_{hcn_pos}")
    else:
        plot_single(f"{task_id}_single", task_id)


def visualize_snapshot(axs, task_id, key_word, overwrite=False):
    from collections import defaultdict
    from tqdm import tqdm
    metrics = ("Vs", "Vd",
               "I_HCN_som", "I_HCN_den",
               "I_GABA_som", "I_GABA_den", )
    def get_the_metric(metric_name):
        all_metric = []
        for ff, ff_data in loaded_data.items():
    
            all_metric.append(np.mean(ff_metric, axis=0))
        all_metric = np.stack(all_metric, axis=0)
        return all_metric

    def plot_metric(ax, metric_name, color, **kwargs):
        metric_values = get_the_metric(metric_name)
        mean_values = np.mean(metric_values, axis=0)
        sem_values = np.std(mean_values, axis=0) / np.sqrt(metric_values.shape[0])
        ts = np.linspace(-1000, 1000, len(mean_values))
        ax.plot(ts, mean_values, color=color, **kwargs)
        ax.fill_between(ts, mean_values + sem_values, mean_values - sem_values, color=color, alpha=0.2, lw=0)
        ax.spines[['right', 'top']].set_visible(False)
        ax.axvline(x=0, ls='--', color='gray', alpha=0.4, lw=1)
        ax.set_xticks((-150, 0, 150))
        ax.set_xlabel("Time (ms)")
        ax.set_xlim(-150, 150)
        ax.legend(frameon=False, fontsize='small', loc='best')


    loaded_data = {}
    for filename in os.listdir(path.join("simulation_result", task_id)):
        if os.path.isdir(path.join("simulation_result", task_id, filename)) and (key_word in filename):
            assert filename not in loaded_data
            cell_path = path.join("simulation_result", task_id, filename)
            if path.exists(path.join(cell_path, "shortcut.npy")) and (not overwrite):
                loaded_data[filename] = np.load(path.join(cell_path, "shortcut.npy"), allow_pickle=True)
                continue
            shortcut = defaultdict([])
            for csv_file in tqdm(os.listdir(cell_path)):
                assert csv_file.endswith(".csv")
                file_index = csv_file.split("_")[0]
                metric = csv_file[len(file_index)+1:-4]
                assert metric in metrics
                certain_metric = np.array(csv_reader_single(path.join(cell_path, csv_file)), dtype=np.float64)
                assert certain_metric.dtype == np.float64, f"{certain_metric}"
                shortcut[metric].append(certain_metric)
            for metric in metrics:
                shortcut[metric] = np.mean(shortcut[metric], axis=0)
            loaded_data[filename] = shortcut
            np.save(path.join(cell_path, "shortcut.npy"), shortcut)

    plot_metric(axs[0], "Vs", color=SOM_COLOR, label='Vs', lw=1, alpha=0.7)
    plot_metric(axs[0], "Vd", color=DEN_COLOR, label='Vd', lw=1, alpha=0.7)
    axs[0].set_ylabel("Vm (mV)")
    axs[1].set_ylim(-60, -40)

    if key_word == "GPe":
        plot_metric(axs[1], "I_GABA_som", color=GPE_COLOR, label='I_GABA_som', lw=2, alpha=0.7)
    else:
        plot_metric(axs[1], "I_GABA_den", color=STR_COLOR, label='I_GABA_den', lw=2, alpha=0.7)
    if "som" in task_id:
        plot_metric(axs[1], "I_HCN_som", color='red', label='I_HCN_som', lw=2, alpha=0.7)
    elif "den" in task_id:
        plot_metric(axs[1], "I_HCN_den", color='blue', label='I_HCN_den', lw=2, alpha=0.7)

    axs[1].set_ylabel("Current (nA)")
    axs[1].set_ylim(-1, 4.5)



def reconcile():
    # baseline_GPe = xlsx_reader(path.join('bio_data', 'OnePulse.xlsx'), "GPe naive")
    # baseline_Str = xlsx_reader(path.join('bio_data', 'OnePulse.xlsx'), "D1 naive")
    # baseline_noHCN_GPe = xlsx_reader(path.join('bio_data', 'OnePulse.xlsx'), "GPeZD")
    # baseline_noHCN_Str = xlsx_reader(path.join('bio_data', 'OnePulse.xlsx'), "D1ZD")
    fig, axs = plt.subplots(2, 2, figsize=(6, 6))
    visualize_snapshot(axs[:, 0], args.task_id, "GPe")
    axs[0, 0].set_title("GPe")
    visualize_snapshot(axs[:, 1], args.task_id, "Str")
    axs[0, 1].set_title("Str")
    fig.savefig(path.join(path.dirname(__file__), "figures", f"{args.task_id}_IPSC.jpg"), dpi=300)
    plt.close(fig)



if __name__ == "__main__":
    args = parse_args()
    reconcile()
    # main(args.task_id, single=args.single, overwrite=args.overwrite)