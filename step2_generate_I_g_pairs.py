import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
import seaborn as sns
import os
import os.path as path
from utils import *

plt.rcParams["font.family"] = "Arial"
plt.rcParams['font.size'] = 10


def generate_I_g_pairs(required_fr):
    # Load data
    g = get_data('prepared_g.bin')
    I = get_data('prepared_I.bin')
    r0 = get_data('prepared_r_0.bin')
    order_I = np.argsort(I)
    print("g: ", g.shape, g[:10])
    print("I: ", I.shape, I[:10])
    print("r0: ", r0.shape, r0[:10])
    gg, II = np.meshgrid(g, I[order_I], indexing='ij')

    fig = plt.figure(figsize=(6, 6))
    h = plt.axes(projection='3d')
    # no HCN baseline
    h.plot_surface(gg, II, np.tile(r0[np.newaxis, order_I], (len(g), 1)),
                   color='gray', edgecolor='none', alpha=0.3)
    h.plot_surface(gg, II, np.tile(r0[np.newaxis, order_I], (len(g), 1))/0.68,
                   color='gray', edgecolor='none', alpha=0.3)
    required_fr_zero = required_fr*0.68
    required_I = np.interp(required_fr_zero, r0, I)
    required_r = np.interp(required_fr_zero, r0, r0)
    write_data(f"selected_I_HCN_zero.bin", required_I)
    write_data(f"selected_g_HCN_zero.bin", np.zeros_like(required_I))
    write_data(f"selected_r_HCN_zero.bin", required_r)

    def optimal_g_value(file_name, keyword, cmap):
        rx = get_data(file_name)
        rx = rx.reshape((len(g), len(I)))

        surf = h.plot_surface(gg, II, rx[:, order_I], cmap=cmap, edgecolor='none', alpha=0.7)
        cbar = fig.colorbar(surf, shrink=0.5, aspect=10)
        cbar.set_label(keyword)
        plt.xlabel(r'$g_{HCN}$ (nS/pF)')
        plt.ylabel(r'$I_{app}$ (pA)')
        plt.title(f'rate (Hz)')

        # Compute tar and find indices
        tar_r = r0 / 0.68
        optim_i = np.argmin(np.abs(tar_r[np.newaxis, :] - rx), axis=0)
        h.plot(g[optim_i][order_I], I[order_I], tar_r[order_I], 'k', zorder=10, alpha=0.5, ls='--')
        # h.plot(g[optim_i][order_I], I[order_I], rx[optim_i, order_I], 'red')
        optimal_g = g[optim_i][order_I]
        optimal_I = I[order_I]
        optimal_rx = rx[optim_i, order_I]
        required_g = np.interp(required_fr, optimal_rx, optimal_g)
        required_I = np.interp(required_fr, optimal_rx, optimal_I)
        required_r = np.interp(required_fr, optimal_rx, optimal_rx)

        write_data(f"selected_g_HCN_{keyword}.bin", required_g)
        write_data(f"selected_I_HCN_{keyword}.bin", required_I)
        write_data(f"selected_r_HCN_{keyword}.bin", required_r)

    # Somatic HCN
    optimal_g_value("prepared_r_som.bin", 'som', 'viridis')
    # Dendritic HCN
    optimal_g_value("prepared_r_den.bin", 'den', "cool")

    h.legend(handles=[mpatches.Patch(color='lightgray', label='R0 (No HCN)'),
                      mpatches.Patch(color='gray', label='R0/0.68'),])
    fig.tight_layout()
    fig.savefig(path.join(path.dirname(__file__), "figures", "grid_search_result.jpg"), dpi=300)
    plt.show()


def plot_frequency_summary(required_fr):
    def compute_naive_frequency(cells_of_trials):
        total_frequency = []
        for row_id, (cell, trials) in enumerate(cells_of_trials.items()):
            total_frequency += [np.sum(trial <= 1000) for trial in trials]
        return total_frequency

    baseline_GPe = xlsx_reader(path.join('bio_data', 'OnePulse.xlsx'), "GPe naive")
    baseline_Str = xlsx_reader(path.join('bio_data', 'OnePulse.xlsx'), "D1 naive")
    naive_GPe_fr = compute_naive_frequency(baseline_GPe)
    naive_Str_fr = compute_naive_frequency(baseline_Str)
    generated_som_fr = get_data(f'selected_r_HCN_som.bin')
    generated_den_fr = get_data(f'selected_r_HCN_den.bin')
    generated_zero_fr = get_data(f'selected_r_HCN_zero.bin')

    bins = np.linspace(0, 100, 51)
    fig, axs = plt.subplots(1, 3, sharex='all', figsize=(10, 3))
    bio_data_sets = {
        "GPe dataset baseline": (naive_GPe_fr, GPE_COLOR),
        "Str dataset baseline": (naive_Str_fr, STR_COLOR)
    }
    for name, (data, color) in bio_data_sets.items():
        sns.histplot(data, bins=bins, label=name, color=color,
                     stat='density', kde=True, ax=axs[0],
                     linewidth=0, line_kws={'linewidth': 1})

    axs[0].set_title("Bio Data")
    axs[0].spines[['right', 'top']].set_visible(False)
    axs[0].legend()

    target_data_sets = {
        "Target Normal": (required_fr, 'black')
    }
    for name, (data, color) in target_data_sets.items():
        sns.histplot(data, bins=bins, label=name, color=color,
                     stat='density', kde=True, ax=axs[1],
                     linewidth=0, line_kws={'linewidth': 1})

    axs[1].set_title("Target Normal Distribution")
    axs[1].spines[['right', 'top']].set_visible(False)
    axs[1].legend()

    generated_data_sets = {
        "Soma HCN": (generated_som_fr, SOM_COLOR),
        "Dendrite HCN": (generated_den_fr, DEN_COLOR),
        "No HCN": (generated_zero_fr, KO_COLOR)
    }
    for name, (data, color) in generated_data_sets.items():
        sns.histplot(data, bins=bins, label=name, color=color,
                     stat='density', kde=True, ax=axs[2],
                     linewidth=0, line_kws={'linewidth': 1})

    axs[2].set_title("Generated Data")
    axs[2].spines[['right', 'top']].set_visible(False)
    axs[2].legend()
    axs[1].sharey(axs[2])

    fig.supxlabel("Baseline Firing Rate [Hz]")

    fig.tight_layout()
    fig.savefig(path.join(path.dirname(__file__), "figures", "frequency_histogram.jpg"), dpi=300)
    plt.show()


def main():
    ###################################################################
    ########## TO Change: Target firing rate distribution #############
    ###################################################################
    NUM_sample = 1000
    MAX_Fr = 60  # Hz
    MIN_Fr = 5  # Hz
    required_fr = np.random.normal(loc=25, scale=12.0, size=NUM_sample*5)
    required_fr = required_fr[required_fr<MAX_Fr]
    required_fr = required_fr[required_fr>MIN_Fr][:NUM_sample]
    print(required_fr.shape, np.mean(required_fr), np.std(required_fr))
    generate_I_g_pairs(required_fr)
    plot_frequency_summary(required_fr)


if __name__ == "__main__":
    main()