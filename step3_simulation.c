#include "bio_data/SNrModel.h"
#include "step0_config.h"
#include <stdio.h>
#include <sys/stat.h>
#include <string.h> // Required for strncpy, strrchr
#include <errno.h>  // Required for errno

#ifdef _WIN32 // For _mkdir on Windows
    #include <direct.h>
    #define MAKE_DIR(path) _mkdir(path)
#else  // For mkdir on macOS/Linux
    #include <sys/types.h>
    #include <sys/stat.h>
    #define MAKE_DIR(path) mkdir(path, 0777)
#endif


int directory_not_exists(const char *path) {
    struct stat info;
    if (stat(path, &info) == 0 && (info.st_mode & S_IFDIR)) {
        return 0; // Directory exists
    }
    return 1;
}

typedef struct {
    double *spike_times;
    double *I_HCN_som;
    double *m_HCN_som;
    double *g_HCN_som;
    double *I_app;
    double *I_SD;
    double *I_DS;
    double *I_TRPC3;
    double *I_HCN_den;
    double *m_HCN_den;
    double *g_HCN_den;
    double *Vs;
    double *Vd;
    double *I_GABA_som;
    double *E_GABA_som;
    double *g_GABA_som;
    double *D;
    double *I_GABA_den;
    double *E_GABA_den;
    double *g_GABA_den;
    double *F;
    int num_spikes;
} Spikes;


void write_csv(const char *filename, double *data, int num) {
    FILE *file = fopen(filename, "w");
    if (file == NULL) {
        printf("Error opening file!\n");
        return;
    }
    for (int i = 0; i < num; i++) {
        fprintf(file, "%f,", data[i]);
    }
    fprintf(file, "END\n");
    fclose(file);
    printf("Result saved in %s \n", filename);
}


// This function creates all directories in a given path.
void create_path(const char *path) {
    char temp_path[512];
    char *p = NULL;
    size_t len;

    // Copy path to a temporary buffer so we can modify it
    strncpy(temp_path, path, sizeof(temp_path));
    temp_path[sizeof(temp_path) - 1] = '\0';
    len = strlen(temp_path);

    // Remove trailing slash if it exists
    if (temp_path[len - 1] == '/') {
        temp_path[len - 1] = '\0';
    }

    // Iterate through the path and create each directory level
    for (p = temp_path + 1; *p; p++) {
        if (*p == '/') {
            *p = '\0'; // Temporarily terminate the string
            if (directory_not_exists(temp_path)) {
                MAKE_DIR(temp_path);
            }
            *p = '/'; // Restore the slash
        }
    }
    // Create the final directory
    if (directory_not_exists(temp_path)) {
        MAKE_DIR(temp_path);
    }
}


Spikes spike_simulation(State *restrict s, int duration, double GPe_stim_time, double Str_stim_time) {
    Spikes spikes;
    spikes.num_spikes = 0;
    spikes.spike_times = (double *)malloc(CONFIG_spikes_init_size * sizeof(double));
    for (int i = 0; i < duration*CONFIG_1ms_step_num; i++) {
        if (i<=GPe_stim_time*CONFIG_1ms_step_num && (i+1)>GPe_stim_time*CONFIG_1ms_step_num) {
            s->GPe_stim = 1;
        } else {
            s->GPe_stim = 0;
        }
        if (i<=Str_stim_time*CONFIG_1ms_step_num && (i+1)>Str_stim_time*CONFIG_1ms_step_num) {
            s->Str_stim = 1;
        } else {
            s->Str_stim = 0;
        }
        if (f(s, CONFIG_dt)) {
            spikes.spike_times[spikes.num_spikes] = s->time;
            spikes.num_spikes++;
        }
    }
    return spikes;
}


Spikes full_simulation(State *restrict s, int duration, double GPe_stim_time, double Str_stim_time) {
    Spikes spikes;
    spikes.num_spikes = 0;
    spikes.spike_times = (double *)malloc(CONFIG_spikes_init_size * sizeof(double));
    spikes.I_HCN_som = (double *)malloc(CONFIG_1ms_step_num*duration * sizeof(double));
    spikes.m_HCN_som = (double *)malloc(CONFIG_1ms_step_num*duration * sizeof(double));
    spikes.g_HCN_som = (double *)malloc(CONFIG_1ms_step_num*duration * sizeof(double));
    spikes.I_app = (double *)malloc(CONFIG_1ms_step_num*duration * sizeof(double));
    spikes.I_SD = (double *)malloc(CONFIG_1ms_step_num*duration * sizeof(double));
    spikes.I_DS = (double *)malloc(CONFIG_1ms_step_num*duration * sizeof(double));
    spikes.I_TRPC3 = (double *)malloc(CONFIG_1ms_step_num*duration * sizeof(double));
    spikes.I_HCN_den = (double *)malloc(CONFIG_1ms_step_num*duration * sizeof(double));
    spikes.m_HCN_den = (double *)malloc(CONFIG_1ms_step_num*duration * sizeof(double));
    spikes.g_HCN_den = (double *)malloc(CONFIG_1ms_step_num*duration * sizeof(double));
    spikes.Vs = (double *)malloc(CONFIG_1ms_step_num*duration * sizeof(double));
    spikes.Vd = (double *)malloc(CONFIG_1ms_step_num*duration * sizeof(double));
    spikes.I_GABA_som = (double *)malloc(CONFIG_1ms_step_num*duration * sizeof(double));
    spikes.E_GABA_som = (double *)malloc(CONFIG_1ms_step_num*duration * sizeof(double));
    spikes.g_GABA_som = (double *)malloc(CONFIG_1ms_step_num*duration * sizeof(double));
    spikes.D = (double *)malloc(CONFIG_1ms_step_num*duration * sizeof(double));
    spikes.I_GABA_den = (double *)malloc(CONFIG_1ms_step_num*duration * sizeof(double));
    spikes.E_GABA_den = (double *)malloc(CONFIG_1ms_step_num*duration * sizeof(double));
    spikes.g_GABA_den = (double *)malloc(CONFIG_1ms_step_num*duration * sizeof(double));
    spikes.F = (double *)malloc(CONFIG_1ms_step_num*duration * sizeof(double));
    for (int i = 0; i < duration*CONFIG_1ms_step_num; i++) {
        if (i<=GPe_stim_time*CONFIG_1ms_step_num && (i+1)>GPe_stim_time*CONFIG_1ms_step_num) {
            s->GPe_stim = 1;
        } else {
            s->GPe_stim = 0;
        }
        if (i<=Str_stim_time*CONFIG_1ms_step_num && (i+1)>Str_stim_time*CONFIG_1ms_step_num) {
            s->Str_stim = 1;
        } else {
            s->Str_stim = 0;
        }
        if (f(s, CONFIG_dt)) {
            spikes.spike_times[spikes.num_spikes] = s->time;
            spikes.num_spikes++;
        }
        spikes.I_HCN_som[i] = s->g_HCN_som * s->m_HCN_som * (s->V_s - E_HCN);
        spikes.m_HCN_som[i] = s->m_HCN_som;
        spikes.g_HCN_som[i] = s->g_HCN_som;
        spikes.I_app[i] = s->I_app;
        spikes.I_SD[i] = g_C / C_den * (s->V_d - s->V_s);
        spikes.I_DS[i] = g_C / C_som * (s->V_s - s->V_d);
        spikes.I_TRPC3[i] = g_TRPC3 * (s->V_d - E_TRPC3);
        spikes.I_HCN_den[i] = s->g_HCN_den * s->m_HCN_den * (s->V_d - E_HCN);
        spikes.m_HCN_den[i] = s->m_HCN_den;
        spikes.g_HCN_den[i] = s->g_HCN_den;
        spikes.Vs[i] = s->V_s;
        spikes.Vd[i] = s->V_d;
        spikes.E_GABA_som[i] = V_T * log((p_Cl * Cl_out + p_HCO3 * HCO3_out) / (p_Cl * s->Cl_som + p_HCO3 * HCO3_in)) / z_GABA;;
        spikes.I_GABA_som[i] = s->g_GABA_som * (s->V_s - spikes.E_GABA_som[i]);
        spikes.g_GABA_som[i] = s->g_GABA_som;
        spikes.D[i] = s->D;
        spikes.E_GABA_den[i] = V_T * log((p_Cl * Cl_out + p_HCO3 * HCO3_out) / (p_Cl * s->Cl_den + p_HCO3 * HCO3_in)) / z_GABA;;
        spikes.I_GABA_den[i] = s->g_GABA_den * (s->V_d - spikes.E_GABA_den[i]);
        spikes.g_GABA_den[i] = s->g_GABA_den;
        spikes.F[i] = s->F;
    }
    return spikes;
}


int batch_simulation(double W_GPe, double W_Str, double tau, const char* HCN,
    double GPe_stim, double Str_stim, const char* task_id, int num_sim) {
    // load conductances
    char g_value_filename[512];
    if (strcmp(HCN, "som") == 0) {
        strcpy(g_value_filename, SAVE_DIR "selected_g_HCN_som.bin");
    } else if (strcmp(HCN, "den") == 0) {
        strcpy(g_value_filename, SAVE_DIR "selected_g_HCN_den.bin");
    } else {
        strcpy(g_value_filename, SAVE_DIR "selected_g_HCN_zero.bin");
    }
    double g_HCN[1024];
    size_t N0;
    printf("%s \n", g_value_filename);
    read_binary_file(g_value_filename, g_HCN, &N0);

    // load current
    char I_value_filename[512];
    if (strcmp(HCN, "som") == 0) {
        strcpy(I_value_filename, SAVE_DIR "selected_I_HCN_som.bin");
    } else if (strcmp(HCN, "den") == 0) {
        strcpy(I_value_filename, SAVE_DIR "selected_I_HCN_den.bin");
    } else {
        strcpy(I_value_filename, SAVE_DIR "selected_I_HCN_zero.bin");
    }
    double I[1024];
    size_t N1;
    printf("%s \n", I_value_filename);
    read_binary_file(I_value_filename, I, &N1);

    // simulate for all possible conductances
    char filename[512];
    strcpy(filename, RESULT_DIR);
    strcat(filename, task_id);
    strcat(filename, ".csv");
    printf("Result writing in %s \n", filename);
    FILE *result = fopen(filename, "w");
    if (result == NULL) {
        perror("Failed to open file");
        return 1;  // Or handle the error as needed
    }
    for (int j = 0; j < num_sim; j++) {
        State s = init_state();
        s.W_GPe = W_GPe;
        s.tau_GABA_som = tau;
        s.W_Str = W_Str;
        s.tau_GABA_den = tau;
        s.I_app = I[j];
        if (strcmp(HCN, "som") == 0) {
            s.g_HCN_som = g_HCN[j];
        } else if (strcmp(HCN, "den") == 0) {
            s.g_HCN_den = g_HCN[j];
        }
        Spikes spikes = spike_simulation(&s, SIM_DURATION_total, GPe_stim, Str_stim);
        printf("#%d: I_app: %f, g_HCN_%s: %f, %d spikes \n", j, I[j], HCN, g_HCN[j], spikes.num_spikes);
        fprintf(result, "%d,", spikes.num_spikes);
        for (int i = 0; i < spikes.num_spikes; i++) {
            fprintf(result, "%f,", spikes.spike_times[i]);
        }
        free(spikes.spike_times);
    }
    fprintf(result, "END\n");
    fclose(result);
    printf("Result saved in %s \n", filename);
    return 0;
}

void write_and_free(const char* path, const char* suffix, double* data, int num_elements) {
    char filename[512];
    snprintf(filename, sizeof(filename), "%s_%s.csv", path, suffix);
    write_csv(filename, data, num_elements);
    free(data);
}


int batch_simulation_with_snapshot_saved(double W_GPe, double W_Str, double tau, const char* HCN,
    double GPe_stim, double Str_stim, const char* task_id, int num_sim) {
    // load conductances
    char g_value_filename[512];
    if (strcmp(HCN, "som") == 0) {
        strcpy(g_value_filename, SAVE_DIR "selected_g_HCN_som.bin");
    } else if (strcmp(HCN, "den") == 0) {
        strcpy(g_value_filename, SAVE_DIR "selected_g_HCN_den.bin");
    } else {
        strcpy(g_value_filename, SAVE_DIR "selected_g_HCN_zero.bin");
    }
    double g_HCN[1024];
    size_t N0;
    printf("%s \n", g_value_filename);
    read_binary_file(g_value_filename, g_HCN, &N0);

    // load current
    char I_value_filename[512];
    if (strcmp(HCN, "som") == 0) {
        strcpy(I_value_filename, SAVE_DIR "selected_I_HCN_som.bin");
    } else if (strcmp(HCN, "den") == 0) {
        strcpy(I_value_filename, SAVE_DIR "selected_I_HCN_den.bin");
    } else {
        strcpy(I_value_filename, SAVE_DIR "selected_I_HCN_zero.bin");
    }
    double I[1024];
    size_t N1;
    printf("%s \n", I_value_filename);
    read_binary_file(I_value_filename, I, &N1);

    // --- FIX 1: Create the output directory for snapshots ---
    char task_path[512];
    snprintf(task_path, sizeof(task_path), "%s%s", RESULT_DIR, task_id);
    create_path(task_path);

    // simulate for all possible conductances
    char filename[512];
    strcpy(filename, RESULT_DIR);
    strcat(filename, task_id);
    strcat(filename, ".csv");
    printf("Result writing in %s \n", filename);
    FILE *result = fopen(filename, "w");
    if (result == NULL) {
        perror("Failed to open file");
        return 1;  // Or handle the error as needed
    }
    for (int j = 0; j < num_sim; j++) {
        State s = init_state();
        s.W_GPe = W_GPe;
        s.tau_GABA_som = tau;
        s.W_Str = W_Str;
        s.tau_GABA_den = tau;
        s.I_app = I[j];
        if (strcmp(HCN, "som") == 0) {
            s.g_HCN_som = g_HCN[j];
        } else if (strcmp(HCN, "den") == 0) {
            s.g_HCN_den = g_HCN[j];
        }

        // --- FIX 2: Call full_simulation instead of spike_simulation ---
        // This ensures all data arrays in the Spikes struct are allocated and populated.
        Spikes spikes = full_simulation(&s, SIM_DURATION_total, GPe_stim, Str_stim);

        printf("#%d: I_app: %f, g_HCN_%s: %f, %d spikes \n", j, I[j], HCN, g_HCN[j], spikes.num_spikes);
        fprintf(result, "%d,", spikes.num_spikes);
        for (int i = 0; i < spikes.num_spikes; i++) {
            fprintf(result, "%f,", spikes.spike_times[i]);
        }
        free(spikes.spike_times);

        // save snapshot
        int data_length = SIM_DURATION_total * CONFIG_1ms_step_num;
        char base_path[512];
        // The path now correctly points inside the newly created directory
        snprintf(base_path, sizeof(base_path), "%s%s/%d", RESULT_DIR, task_id, j);

        write_and_free(base_path, "Vd", spikes.Vd, data_length);
        write_and_free(base_path, "Vs", spikes.Vs, data_length);
        write_and_free(base_path, "I_HCN_som", spikes.I_HCN_som, data_length);
        write_and_free(base_path, "I_HCN_den", spikes.I_HCN_den, data_length);
        write_and_free(base_path, "I_GABA_som", spikes.I_GABA_som, data_length);
        write_and_free(base_path, "I_GABA_den", spikes.I_GABA_den, data_length);

        free(spikes.m_HCN_som);
        free(spikes.g_HCN_som);
        free(spikes.I_app);
        free(spikes.I_SD);
        free(spikes.I_DS);
        free(spikes.I_TRPC3);
        free(spikes.m_HCN_den);
        free(spikes.g_HCN_den);
        free(spikes.E_GABA_som);
        free(spikes.g_GABA_som);
        free(spikes.D);
        free(spikes.E_GABA_den);
        free(spikes.g_GABA_den);
        free(spikes.F);
    }
    fprintf(result, "END\n");
    fclose(result);
    printf("Result saved in %s \n", filename);
    return 0;
}

int single_simulation(double W_GPe, double W_Str, double tau, const char* HCN,
    double GPe_stim, double Str_stim, const char* task_id, double g_HCN, double I_app) {
    // simulate for all possible conductances
    char filename[512];
    strcpy(filename, RESULT_DIR);
    strcat(filename, task_id);
    strcat(filename, ".csv");
    printf("Result writing in %s \n", filename);
    FILE *result = fopen(filename, "w");
    if (result == NULL) {
        perror("Failed to open file");
        return 1;  // Or handle the error as needed
    }

    State s = init_state();
    s.W_GPe = W_GPe;
    s.tau_GABA_som = tau;
    s.W_Str = W_Str;
    s.tau_GABA_den = tau;
    s.I_app = I_app;
    if (strcmp(HCN, "som") == 0) {
        s.g_HCN_som = g_HCN;
    } else if (strcmp(HCN, "den") == 0) {
        s.g_HCN_den = g_HCN;
    }
    Spikes spikes = full_simulation(&s, SIM_DURATION_total, GPe_stim, Str_stim);
    printf("#1: I_app: %f, g_HCN_%s: %f, %d spikes \n", I_app, HCN, g_HCN, spikes.num_spikes);
    fprintf(result, "%d,", spikes.num_spikes);
    for (int i = 0; i < spikes.num_spikes; i++) {
        fprintf(result, "%f,", spikes.spike_times[i]);
    }
    free(spikes.spike_times);

    fprintf(result, "END\n");
    fclose(result);
    printf("Result saved in %s \n", filename);

    int data_length = SIM_DURATION_total * CONFIG_1ms_step_num;
    char base_path[512];
    snprintf(base_path, sizeof(base_path), "%s%s", RESULT_DIR, task_id);

    write_and_free(base_path, "I_app", spikes.I_app, data_length);
    write_and_free(base_path, "I_SD", spikes.I_SD, data_length);
    write_and_free(base_path, "I_DS", spikes.I_DS, data_length);
    write_and_free(base_path, "I_TRPC3", spikes.I_TRPC3, data_length);

    write_and_free(base_path, "I_HCN_som", spikes.I_HCN_som, data_length);
    write_and_free(base_path, "m_HCN_som", spikes.m_HCN_som, data_length);
    write_and_free(base_path, "g_HCN_som", spikes.g_HCN_som, data_length);

    write_and_free(base_path, "I_HCN_den", spikes.I_HCN_den, data_length);
    write_and_free(base_path, "m_HCN_den", spikes.m_HCN_den, data_length);
    write_and_free(base_path, "g_HCN_den", spikes.g_HCN_den, data_length);

    write_and_free(base_path, "Vd", spikes.Vd, data_length);
    write_and_free(base_path, "Vs", spikes.Vs, data_length);

    write_and_free(base_path, "I_GABA_som", spikes.I_GABA_som, data_length);
    write_and_free(base_path, "E_GABA_som", spikes.E_GABA_som, data_length);
    write_and_free(base_path, "g_GABA_som", spikes.g_GABA_som, data_length);
    write_and_free(base_path, "D", spikes.D, data_length);

    write_and_free(base_path, "I_GABA_den", spikes.I_GABA_den, data_length);
    write_and_free(base_path, "E_GABA_den", spikes.E_GABA_den, data_length);
    write_and_free(base_path, "g_GABA_den", spikes.g_GABA_den, data_length);
    write_and_free(base_path, "F", spikes.F, data_length);
    return 0;
}


int main(int argc, char *argv[]) {
    struct timeval start_time, stop_time, elapsed_time;
    gettimeofday(&start_time, NULL);

    char HCN_choice[8] = "zero", task_id[128] = "test/mitten";
    int num_sim = NUM_samples;
    double W_GPe = 0, W_Str = 0, tau = 0;
    double GPe_stim = 1000, Str_stim = 1000;
    double g_HCN = DEFAULT_g_HCN;
    double I_app = DEFAULT_I_app;
    // --- CHANGE 1: Add a variable for the toggle (default is off) ---
    int save_snapshot = 0;

    // --- CHANGE 2: Make the argument parsing loop more flexible ---
    for (int i = 1; i < argc; ) {
        if (strcmp(argv[i], "-GPe") == 0) {
            W_GPe = strtod(argv[i + 1], NULL);
            i += 2;
        } else if (strcmp(argv[i], "-Str") == 0) {
            W_Str = strtod(argv[i + 1], NULL);
            i += 2;
        } else if (strcmp(argv[i], "-tau") == 0) {
            tau = strtod(argv[i + 1], NULL);
            i += 2;
        } else if (strcmp(argv[i], "-GPe_stim") == 0) {
            GPe_stim = strtod(argv[i + 1], NULL);
            i += 2;
        } else if (strcmp(argv[i], "-Str_stim") == 0) {
            Str_stim = strtod(argv[i + 1], NULL);
            i += 2;
        } else if (strcmp(argv[i], "-g_HCN") == 0) {
            g_HCN = strtod(argv[i + 1], NULL);
            i += 2;
        } else if (strcmp(argv[i], "-I_app") == 0) {
            I_app = strtod(argv[i + 1], NULL);
            i += 2;
        } else if (strcmp(argv[i], "-num") == 0) {
            num_sim = strtol(argv[i + 1], NULL, 10);
            i += 2;
        } else if (strcmp(argv[i], "-HCN") == 0) {
            strncpy(HCN_choice, argv[i + 1], sizeof(HCN_choice) - 1);
            HCN_choice[sizeof(HCN_choice) - 1] = '\0';
            i += 2;
        } else if (strcmp(argv[i], "-o") == 0) {
            strncpy(task_id, argv[i + 1], sizeof(task_id) - 1);
            task_id[sizeof(task_id) - 1] = '\0';
            i += 2;
        // --- CHANGE 3: Check for the new '-snapshot' toggle ---
        } else if (strcmp(argv[i], "-snapshot") == 0) {
            save_snapshot = 1; // Enable snapshot saving
            i += 1; // Increment by 1 for a standalone flag
        } else {
            printf("Unimplemented option: %s\n", argv[i]);
            i++; // Move to the next argument
        }
    }

    printf("##########################\n");
    printf("############# Hyperparameters \n");
    printf("##########################\n");
    printf("W_GPe: %f\n", W_GPe);
    printf("W_Str: %f\n", W_Str);
    printf("tau: %f\n", tau);
    printf("GPe_stim: %f\n", GPe_stim);
    printf("Str_stim: %f\n", Str_stim);
    printf("NUM_simulation: %d\n", num_sim);
    printf("HCN_choice: %s\n", HCN_choice);
    printf("task_id: %s\n", task_id);
    // --- CHANGE 4: Print the status of the new toggle ---
    printf("Save Snapshots: %s\n", save_snapshot ? "Yes" : "No");

    if (num_sim == 1) {
        printf("g_HCN: %f\n", g_HCN);
        printf("I_app: %f\n", I_app);
        printf("\n");
        printf("single simulation begins \n");
        char task_path[512];
        strcpy(task_path, RESULT_DIR);
        strcat(task_path, task_id);
        create_path(task_path);

        strcat(task_id, "/single");
        single_simulation(W_GPe, W_Str, tau, HCN_choice, GPe_stim, Str_stim, task_id, g_HCN, I_app);
        printf("single finishes \n");
    } else {
        printf("\n");
        // --- CHANGE 5: Use the toggle to decide which function to call ---
        if (save_snapshot) {
            printf("batch simulation with snapshot begins \n");
            // Assuming you have applied the previous fix to this function
            batch_simulation_with_snapshot_saved(W_GPe, W_Str, tau, HCN_choice, GPe_stim, Str_stim, task_id, num_sim);
            printf("batch with snapshot finishes \n");
        } else {
            printf("batch simulation begins \n");
            batch_simulation(W_GPe, W_Str, tau, HCN_choice, GPe_stim, Str_stim, task_id, num_sim);
            printf("batch finishes \n");
        }
    }

    gettimeofday(&stop_time, NULL);
    timersub(&stop_time, &start_time, &elapsed_time);
    printf("##########################\n");
    printf("############# Total time was %f seconds. \n", elapsed_time.tv_sec + elapsed_time.tv_usec * 1e-6);
    printf("##########################\n");
    printf("\n");
    return 0;
}